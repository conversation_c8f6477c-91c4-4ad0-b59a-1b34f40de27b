import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CurrentUser } from '../common/decorators/current-user.decorator.js';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard.js';
import type { User } from '../database/schema/users.schema.js';
import { SendTestEmailDto } from './dto/send-test-email.dto.js';
import { EmailService } from './email.service.js';

@ApiTags('Email')
@Controller('email')
export class EmailController {
  constructor(private readonly emailService: EmailService) {}

  @Post('/test')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: 'Send a test email',
    description: 'Send a test email to verify email configuration. Only available to authenticated users.'
  })
  @ApiResponse({ status: 200, description: 'Test email sent successfully' })
  @ApiResponse({ status: 400, description: 'Failed to send test email' })
  async sendTestEmail(
    @CurrentUser() user: User,
    @Body() dto: SendTestEmailDto
  ): Promise<{ success: boolean; message: string }> {
    const success = await this.emailService.sendTestEmail(
      dto.to || user.email,
      dto.subject,
      dto.template,
      dto.context
    );

    return {
      success,
      message: success 
        ? 'Test email sent successfully' 
        : 'Failed to send test email. Check server logs for details.',
    };
  }

  @Post('/test/welcome')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: 'Send a test welcome email',
    description: 'Send a test welcome email with verification link'
  })
  @ApiResponse({ status: 200, description: 'Test welcome email sent successfully' })
  async sendTestWelcomeEmail(
    @CurrentUser() user: User,
    @Body() dto: { to?: string }
  ): Promise<{ success: boolean; message: string }> {
    const success = await this.emailService.sendWelcomeEmail(
      dto.to || user.email,
      user.name || 'Test User',
      'test-verification-token-123'
    );

    return {
      success,
      message: success 
        ? 'Test welcome email sent successfully' 
        : 'Failed to send test welcome email. Check server logs for details.',
    };
  }

  @Post('/test/password-reset')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: 'Send a test password reset email',
    description: 'Send a test password reset email'
  })
  @ApiResponse({ status: 200, description: 'Test password reset email sent successfully' })
  async sendTestPasswordResetEmail(
    @CurrentUser() user: User,
    @Body() dto: { to?: string }
  ): Promise<{ success: boolean; message: string }> {
    const success = await this.emailService.sendPasswordReset(
      dto.to || user.email,
      user.name || 'Test User',
      'test-reset-token-123'
    );

    return {
      success,
      message: success 
        ? 'Test password reset email sent successfully' 
        : 'Failed to send test password reset email. Check server logs for details.',
    };
  }

  @Post('/test/2fa-enabled')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: 'Send a test 2FA enabled email',
    description: 'Send a test 2FA enabled notification email'
  })
  @ApiResponse({ status: 200, description: 'Test 2FA enabled email sent successfully' })
  async sendTest2FAEnabledEmail(
    @CurrentUser() user: User,
    @Body() dto: { to?: string }
  ): Promise<{ success: boolean; message: string }> {
    const testBackupCodes = [
      'ABC123DEF',
      'GHI456JKL',
      'MNO789PQR',
      'STU012VWX',
      'YZ345ABC6',
      'DEF789GHI',
      'JKL012MNO',
      'PQR345STU'
    ];

    const success = await this.emailService.send2FAEnabled(
      dto.to || user.email,
      user.name || 'Test User',
      testBackupCodes
    );

    return {
      success,
      message: success 
        ? 'Test 2FA enabled email sent successfully' 
        : 'Failed to send test 2FA enabled email. Check server logs for details.',
    };
  }

  @Post('/test/new-device')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: 'Send a test new device login email',
    description: 'Send a test new device login notification email'
  })
  @ApiResponse({ status: 200, description: 'Test new device email sent successfully' })
  async sendTestNewDeviceEmail(
    @CurrentUser() user: User,
    @Body() dto: { to?: string }
  ): Promise<{ success: boolean; message: string }> {
    const testDeviceInfo = {
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      platform: 'macOS',
      browser: 'Chrome',
      ipAddress: '*************',
    };

    const success = await this.emailService.sendNewDeviceLogin(
      dto.to || user.email,
      user.name || 'Test User',
      testDeviceInfo,
      'San Francisco, CA, USA'
    );

    return {
      success,
      message: success 
        ? 'Test new device email sent successfully' 
        : 'Failed to send test new device email. Check server logs for details.',
    };
  }
}
