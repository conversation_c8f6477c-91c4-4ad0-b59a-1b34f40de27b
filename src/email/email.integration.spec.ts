import { MailerService } from '@nestjs-modules/mailer';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { EmailModule } from './email.module';
import { EmailService } from './email.service';

describe('Email Integration Tests', () => {
  let module: TestingModule;
  let emailService: EmailService;
  let mailerService: MailerService;
  let configService: ConfigService;

  beforeEach(async () => {
    // Set test environment
    process.env.NODE_ENV = 'test';
    process.env.EMAIL_BASE_URL = 'https://test.rsglider.com';
    process.env.EMAIL_FROM_NAME = 'RSGlider Test';
    process.env.SMTP_FROM = '<EMAIL>';
    process.env.SUPPORT_EMAIL = '<EMAIL>';

    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: ['.env.test'],
        }),
        EmailModule,
      ],
    }).compile();

    emailService = module.get<EmailService>(EmailService);
    mailerService = module.get<MailerService>(MailerService);
    configService = module.get<ConfigService>(ConfigService);

    // Mock the mailerService.sendMail method
    jest.spyOn(mailerService, 'sendMail').mockResolvedValue(undefined);
  });

  afterEach(async () => {
    await module.close();
    jest.restoreAllMocks();
  });

  it('should be properly configured', () => {
    expect(emailService).toBeDefined();
    expect(mailerService).toBeDefined();
    expect(configService).toBeDefined();
  });

  describe('Email Service Integration', () => {
    it('should send welcome email with proper configuration', async () => {
      const result = await emailService.sendWelcomeEmail(
        '<EMAIL>',
        'John Doe',
        'verification-token-123'
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Welcome to RSGlider - Verify Your Email',
        template: 'welcome',
        context: {
          name: 'John Doe',
          verificationUrl: 'https://test.rsglider.com/auth/verify-email?token=verification-token-123',
          verificationToken: 'verification-token-123',
          baseUrl: 'https://test.rsglider.com',
          siteName: 'RSGlider Test',
          currentYear: new Date().getFullYear(),
          supportEmail: '<EMAIL>',
        },
        attachments: undefined,
        cc: undefined,
        bcc: undefined,
      });
    });

    it('should send password reset email with proper configuration', async () => {
      const result = await emailService.sendPasswordReset(
        '<EMAIL>',
        'Jane Smith',
        'reset-token-456'
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Reset Your Password',
        template: 'password-reset',
        context: {
          name: 'Jane Smith',
          resetUrl: 'https://test.rsglider.com/auth/reset-password?token=reset-token-456',
          resetToken: 'reset-token-456',
          expiresIn: '1 hour',
          baseUrl: 'https://test.rsglider.com',
          siteName: 'RSGlider Test',
          currentYear: new Date().getFullYear(),
          supportEmail: '<EMAIL>',
        },
        attachments: undefined,
        cc: undefined,
        bcc: undefined,
      });
    });

    it('should send 2FA enabled notification with backup codes', async () => {
      const backupCodes = [
        'ABC123DEF',
        'GHI456JKL',
        'MNO789PQR',
        'STU012VWX',
        'YZ345ABC6',
        'DEF789GHI',
        'JKL012MNO',
        'PQR345STU'
      ];

      const result = await emailService.send2FAEnabled(
        '<EMAIL>',
        'Bob Johnson',
        backupCodes
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Two-Factor Authentication Enabled',
        template: '2fa-enabled',
        context: {
          name: 'Bob Johnson',
          enabledTime: expect.any(Date),
          backupCodes,
          baseUrl: 'https://test.rsglider.com',
          siteName: 'RSGlider Test',
          currentYear: new Date().getFullYear(),
          supportEmail: '<EMAIL>',
        },
        attachments: undefined,
        cc: undefined,
        bcc: undefined,
      });
    });

    it('should send new device login notification with location', async () => {
      const deviceInfo = {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        platform: 'Windows',
        browser: 'Edge',
        ipAddress: '***********',
      };

      const result = await emailService.sendNewDeviceLogin(
        '<EMAIL>',
        'Alice Brown',
        deviceInfo,
        'New York, NY, USA'
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'New Device Login Detected',
        template: 'new-device-login',
        context: {
          name: 'Alice Brown',
          deviceInfo,
          location: 'New York, NY, USA',
          loginTime: expect.any(Date),
          ipAddress: '***********',
          baseUrl: 'https://test.rsglider.com',
          siteName: 'RSGlider Test',
          currentYear: new Date().getFullYear(),
          supportEmail: '<EMAIL>',
        },
        attachments: undefined,
        cc: undefined,
        bcc: undefined,
      });
    });

    it('should send security alert with details', async () => {
      const alertDetails = {
        action: 'Multiple failed login attempts',
        ipAddress: '************',
        timestamp: new Date(),
        userAgent: 'Suspicious Bot/1.0',
      };

      const result = await emailService.sendSecurityAlert(
        '<EMAIL>',
        'Charlie Wilson',
        'Brute Force Attack',
        alertDetails
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Security Alert: Brute Force Attack',
        template: 'security-alert',
        context: {
          name: 'Charlie Wilson',
          alertType: 'Brute Force Attack',
          details: alertDetails,
          alertTime: expect.any(Date),
          baseUrl: 'https://test.rsglider.com',
          siteName: 'RSGlider Test',
          currentYear: new Date().getFullYear(),
          supportEmail: '<EMAIL>',
        },
        attachments: undefined,
        cc: undefined,
        bcc: undefined,
      });
    });

    it('should handle email sending failures gracefully', async () => {
      // Mock a failure
      jest.spyOn(mailerService, 'sendMail').mockRejectedValue(new Error('SMTP Error'));

      const result = await emailService.sendTestEmail('<EMAIL>');

      expect(result).toBe(false);
      expect(mailerService.sendMail).toHaveBeenCalled();
    });
  });

  describe('Configuration Integration', () => {
    it('should use environment variables for configuration', () => {
      expect(configService.get('EMAIL_BASE_URL')).toBe('https://test.rsglider.com');
      expect(configService.get('EMAIL_FROM_NAME')).toBe('RSGlider Test');
      expect(configService.get('SMTP_FROM')).toBe('<EMAIL>');
      expect(configService.get('SUPPORT_EMAIL')).toBe('<EMAIL>');
    });

    it('should use default values when environment variables are not set', () => {
      // Test with a fresh config service that doesn't have the env vars
      const defaultBaseUrl = configService.get('EMAIL_BASE_URL', 'http://localhost:3000');
      const defaultFromName = configService.get('EMAIL_FROM_NAME', 'RSGlider');
      const defaultSupportEmail = configService.get('SUPPORT_EMAIL', '<EMAIL>');

      // Since we set the env vars, these should return the set values
      expect(defaultBaseUrl).toBe('https://test.rsglider.com');
      expect(defaultFromName).toBe('RSGlider Test');
      expect(defaultSupportEmail).toBe('<EMAIL>');
    });
  });

  describe('Context Merging Integration', () => {
    it('should properly merge custom context with default context', async () => {
      const customContext = {
        customVariable: 'custom value',
        userName: 'Custom User',
        baseUrl: 'should-be-overridden',
      };

      const result = await emailService.sendEmail({
        to: '<EMAIL>',
        subject: 'Test Subject',
        template: 'test',
        context: customContext,
      });

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Test Subject',
        template: 'test',
        context: {
          customVariable: 'custom value',
          userName: 'Custom User',
          baseUrl: 'https://test.rsglider.com', // Should be overridden by config
          siteName: 'RSGlider Test',
          currentYear: new Date().getFullYear(),
          supportEmail: '<EMAIL>',
        },
        attachments: undefined,
        cc: undefined,
        bcc: undefined,
      });
    });
  });

  describe('Multiple Recipients Integration', () => {
    it('should handle multiple recipients correctly', async () => {
      const recipients = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

      const result = await emailService.sendEmail({
        to: recipients,
        subject: 'Bulk Email Test',
        template: 'test',
        context: { message: 'Bulk email message' },
      });

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: recipients,
        subject: 'Bulk Email Test',
        template: 'test',
        context: expect.objectContaining({
          message: 'Bulk email message',
          baseUrl: 'https://test.rsglider.com',
          siteName: 'RSGlider Test',
        }),
        attachments: undefined,
        cc: undefined,
        bcc: undefined,
      });
    });
  });

  describe('Email with Attachments Integration', () => {
    it('should handle email attachments correctly', async () => {
      const attachments = [
        {
          filename: 'document.pdf',
          content: Buffer.from('PDF content'),
          contentType: 'application/pdf',
        },
        {
          filename: 'image.png',
          path: '/path/to/image.png',
          contentType: 'image/png',
        },
      ];

      const result = await emailService.sendEmail({
        to: '<EMAIL>',
        subject: 'Email with Attachments',
        template: 'test',
        attachments,
      });

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Email with Attachments',
        template: 'test',
        context: expect.any(Object),
        attachments,
        cc: undefined,
        bcc: undefined,
      });
    });
  });
});
